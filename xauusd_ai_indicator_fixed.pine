//@version=5
indicator("XAUUSD AI Trading Signals", shorttitle="XAUUSD AI", overlay=true)

// ============================================================================
// INPUT PARAMETERS
// ============================================================================

// AI Signal Configuration
ai_enabled = input.bool(true, "Enable AI Signals", group="AI Configuration")
api_url = input.string("http://localhost:8000", "API URL", group="AI Configuration")

// Signal Display Settings
show_buy_signals = input.bool(true, "Show Buy Signals", group="Display")
show_sell_signals = input.bool(true, "Show Sell Signals", group="Display")
show_confidence = input.bool(true, "Show Confidence Level", group="Display")

// Signal Filtering
min_confidence = input.float(0.6, "Minimum Confidence", minval=0.0, maxval=1.0, step=0.05, group="Filtering")
max_signals_per_day = input.int(10, "Max Signals Per Day", minval=1, maxval=50, group="Filtering")

// Visual Settings
buy_color = input.color(color.new(color.green, 0), "Buy Signal Color", group="Colors")
sell_color = input.color(color.new(color.red, 0), "Sell Signal Color", group="Colors")

// Alert Settings
enable_alerts = input.bool(true, "Enable Alerts", group="Alerts")

// ============================================================================
// VARIABLES
// ============================================================================

// Signal tracking
var signal_count_today = 0
var last_signal_type = ""
var current_confidence = 0.0

// ============================================================================
// TECHNICAL INDICATORS (for context and validation)
// ============================================================================

// Moving Averages
sma_20 = ta.sma(close, 20)
sma_50 = ta.sma(close, 50)

// RSI
rsi = ta.rsi(close, 14)

// MACD
[macd_line, signal_line, histogram] = ta.macd(close, 12, 26, 9)

// Bollinger Bands
[bb_upper, bb_middle, bb_lower] = ta.bb(close, 20, 2)

// ATR
atr = ta.atr(14)

// ============================================================================
// AI SIGNAL SIMULATION
// ============================================================================

// Market Context Analysis
trend_bullish = close > sma_50
rsi_oversold = rsi < 30
rsi_overbought = rsi > 70
macd_bullish = macd_line > signal_line

// Simulated AI Signal Logic
ai_buy_condition = trend_bullish and macd_bullish and rsi_oversold and not rsi_overbought
ai_sell_condition = not trend_bullish and not macd_bullish and rsi_overbought and not rsi_oversold

// Confidence calculation
base_confidence = 0.5
trend_confidence = trend_bullish ? 0.2 : -0.2
momentum_confidence = macd_bullish ? 0.15 : -0.15
rsi_confidence = (rsi_oversold or rsi_overbought) ? 0.15 : 0.0

simulated_confidence = math.abs(base_confidence + trend_confidence + momentum_confidence + rsi_confidence)
simulated_confidence := math.min(simulated_confidence, 1.0)

// ============================================================================
// SIGNAL PROCESSING
// ============================================================================

// Reset daily counter
if dayofweek != dayofweek[1]
    signal_count_today := 0

// Process AI signals
var should_show_buy = false
var should_show_sell = false
var signal_confidence = 0.0

if ai_enabled
    // Check for new signals
    new_buy_signal = ai_buy_condition and not ai_buy_condition[1] and simulated_confidence >= min_confidence
    new_sell_signal = ai_sell_condition and not ai_sell_condition[1] and simulated_confidence >= min_confidence
    
    // Limit signals per day
    if (new_buy_signal or new_sell_signal) and signal_count_today < max_signals_per_day
        if new_buy_signal and show_buy_signals
            should_show_buy := true
            signal_confidence := simulated_confidence
            last_signal_type := "BUY"
            signal_count_today += 1
            
        else if new_sell_signal and show_sell_signals
            should_show_sell := true
            signal_confidence := simulated_confidence
            last_signal_type := "SELL"
            signal_count_today += 1

// ============================================================================
// VISUAL ELEMENTS
// ============================================================================

// Plot moving averages for context
plot(sma_20, "SMA 20", color=color.new(color.orange, 50), linewidth=1)
plot(sma_50, "SMA 50", color=color.new(color.purple, 50), linewidth=2)

// Plot Bollinger Bands
p1 = plot(bb_upper, "BB Upper", color=color.new(color.gray, 70))
p2 = plot(bb_lower, "BB Lower", color=color.new(color.gray, 70))
fill(p1, p2, color=color.new(color.gray, 95), title="BB Background")

// Buy Signals
plotshape(
    should_show_buy, 
    title="AI Buy Signal",
    location=location.belowbar,
    color=buy_color,
    style=shape.labelup,
    text="BUY AI",
    textcolor=color.white,
    size=size.normal
)

// Sell Signals
plotshape(
    should_show_sell,
    title="AI Sell Signal", 
    location=location.abovebar,
    color=sell_color,
    style=shape.labeldown,
    text="SELL AI",
    textcolor=color.white,
    size=size.normal
)

// Confidence Level Display
if show_confidence and (should_show_buy or should_show_sell)
    confidence_text = "Confidence: " + str.tostring(signal_confidence * 100, "#.#") + "%"
    label.new(
        bar_index,
        should_show_buy ? low - atr : high + atr,
        confidence_text,
        color=color.new(color.blue, 70),
        textcolor=color.white,
        style=should_show_buy ? label.style_label_up : label.style_label_down,
        size=size.small
    )

// ============================================================================
// INFORMATION TABLE
// ============================================================================

// Create information table
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 6, bgcolor=color.new(color.white, 80), border_width=1)
    
    // Table data
    table.cell(info_table, 0, 0, "XAUUSD AI Signals", text_color=color.black, text_size=size.normal)
    table.cell(info_table, 1, 0, "", text_color=color.black)
    
    table.cell(info_table, 0, 1, "Status:", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 1, ai_enabled ? "Active" : "Disabled", text_color=ai_enabled ? color.green : color.red, text_size=size.small)
    
    table.cell(info_table, 0, 2, "Timeframe:", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 2, timeframe.period, text_color=color.black, text_size=size.small)
    
    table.cell(info_table, 0, 3, "Last Signal:", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 3, last_signal_type, text_color=last_signal_type == "BUY" ? color.green : color.red, text_size=size.small)
    
    table.cell(info_table, 0, 4, "Signals Today:", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 4, str.tostring(signal_count_today), text_color=color.black, text_size=size.small)
    
    table.cell(info_table, 0, 5, "Current RSI:", text_color=color.black, text_size=size.small)
    rsi_color = rsi > 70 ? color.red : rsi < 30 ? color.green : color.black
    table.cell(info_table, 1, 5, str.tostring(rsi, "#.#"), text_color=rsi_color, text_size=size.small)

// ============================================================================
// ALERTS
// ============================================================================

// Buy Alert
if enable_alerts and should_show_buy
    alert_message = "XAUUSD AI BUY SIGNAL - Price: " + str.tostring(close, "#.##") + " - Confidence: " + str.tostring(signal_confidence * 100, "#.#") + "%"
    alert(alert_message, alert.freq_once_per_bar)

// Sell Alert  
if enable_alerts and should_show_sell
    alert_message = "XAUUSD AI SELL SIGNAL - Price: " + str.tostring(close, "#.##") + " - Confidence: " + str.tostring(signal_confidence * 100, "#.#") + "%"
    alert(alert_message, alert.freq_once_per_bar)
