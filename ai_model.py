"""
AI Model for XAUUSD Trading Signal Generation
"""
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Input, MultiHeadAttention, LayerNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from typing import Tuple, Dict, List, Optional
import joblib
import os
from loguru import logger
from config import settings, MODEL_CONFIGS


class XAUUSDPredictor:
    """AI Model for XAUUSD trading signal prediction"""
    
    def __init__(self, model_type: str = "lstm"):
        self.model_type = model_type
        self.sequence_length = settings.SEQUENCE_LENGTH
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.is_trained = False
        
    def prepare_sequences(self, data: pd.DataFrame, target_col: str = 'binary_signal') -> <PERSON>ple[np.ndarray, np.ndarray]:
        """Prepare sequences for LSTM/Transformer training"""
        try:
            # Select feature columns (exclude target and non-numeric columns)
            exclude_cols = ['datetime', 'signal', 'binary_signal', 'price_movement']
            self.feature_columns = [col for col in data.columns if col not in exclude_cols]
            
            # Prepare features and target
            features = data[self.feature_columns].values
            target = data[target_col].values
            
            # Scale features
            features_scaled = self.scaler.fit_transform(features)
            
            # Create sequences
            X, y = [], []
            for i in range(self.sequence_length, len(features_scaled)):
                X.append(features_scaled[i-self.sequence_length:i])
                y.append(target[i])
            
            X = np.array(X)
            y = np.array(y)
            
            logger.info(f"Prepared sequences: X shape {X.shape}, y shape {y.shape}")
            return X, y
            
        except Exception as e:
            logger.error(f"Error preparing sequences: {e}")
            return np.array([]), np.array([])
    
    def build_lstm_model(self, input_shape: Tuple[int, int]) -> Sequential:
        """Build LSTM model architecture"""
        try:
            model = Sequential()
            config = MODEL_CONFIGS["lstm"]
            
            # Add LSTM layers
            for i, layer_config in enumerate(config["layers"]):
                if layer_config["type"] == "lstm":
                    if i == 0:  # First layer needs input_shape
                        model.add(LSTM(
                            units=layer_config["units"],
                            return_sequences=layer_config["return_sequences"],
                            input_shape=input_shape
                        ))
                    else:
                        model.add(LSTM(
                            units=layer_config["units"],
                            return_sequences=layer_config["return_sequences"]
                        ))
                    
                    if "dropout" in layer_config:
                        model.add(Dropout(layer_config["dropout"]))
                        
                elif layer_config["type"] == "dense":
                    model.add(Dense(
                        units=layer_config["units"],
                        activation=layer_config.get("activation", "relu")
                    ))
            
            # Compile model
            model.compile(
                optimizer=config["optimizer"],
                loss=config["loss"],
                metrics=config["metrics"]
            )
            
            logger.info("LSTM model built successfully")
            return model
            
        except Exception as e:
            logger.error(f"Error building LSTM model: {e}")
            return None
    
    def build_transformer_model(self, input_shape: Tuple[int, int]) -> Model:
        """Build Transformer model architecture"""
        try:
            config = MODEL_CONFIGS["transformer"]
            
            # Input layer
            inputs = Input(shape=input_shape)
            
            # Multi-head attention layers
            x = inputs
            for _ in range(config["num_layers"]):
                # Multi-head attention
                attention_output = MultiHeadAttention(
                    num_heads=config["num_heads"],
                    key_dim=config["d_model"] // config["num_heads"]
                )(x, x)
                
                # Add & Norm
                x = LayerNormalization()(x + attention_output)
                
                # Feed forward
                ff_output = Dense(config["dff"], activation="relu")(x)
                ff_output = Dense(config["d_model"])(ff_output)
                ff_output = Dropout(config["dropout_rate"])(ff_output)
                
                # Add & Norm
                x = LayerNormalization()(x + ff_output)
            
            # Global average pooling and output
            x = tf.keras.layers.GlobalAveragePooling1D()(x)
            x = Dense(64, activation="relu")(x)
            x = Dropout(0.2)(x)
            outputs = Dense(1, activation="sigmoid")(x)
            
            model = Model(inputs=inputs, outputs=outputs)
            
            # Compile model
            model.compile(
                optimizer=config["optimizer"],
                loss=config["loss"],
                metrics=config["metrics"]
            )
            
            logger.info("Transformer model built successfully")
            return model
            
        except Exception as e:
            logger.error(f"Error building Transformer model: {e}")
            return None
    
    def train(self, data: pd.DataFrame, validation_split: float = 0.2) -> Dict:
        """Train the AI model"""
        try:
            # Prepare data
            X, y = self.prepare_sequences(data)
            
            if len(X) == 0:
                logger.error("No data available for training")
                return {}
            
            # Split data
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=validation_split, random_state=42, stratify=y
            )
            
            # Build model
            input_shape = (X.shape[1], X.shape[2])
            
            if self.model_type == "lstm":
                self.model = self.build_lstm_model(input_shape)
            elif self.model_type == "transformer":
                self.model = self.build_transformer_model(input_shape)
            else:
                logger.error(f"Unsupported model type: {self.model_type}")
                return {}
            
            if self.model is None:
                logger.error("Failed to build model")
                return {}
            
            # Callbacks
            callbacks = [
                EarlyStopping(patience=10, restore_best_weights=True),
                ReduceLROnPlateau(factor=0.5, patience=5),
                ModelCheckpoint(
                    settings.MODEL_PATH,
                    save_best_only=True,
                    monitor='val_accuracy'
                )
            ]
            
            # Train model
            logger.info("Starting model training...")
            history = self.model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=100,
                batch_size=32,
                callbacks=callbacks,
                verbose=1
            )
            
            # Evaluate model
            train_pred = (self.model.predict(X_train) > 0.5).astype(int)
            val_pred = (self.model.predict(X_val) > 0.5).astype(int)
            
            metrics = {
                'train_accuracy': accuracy_score(y_train, train_pred),
                'val_accuracy': accuracy_score(y_val, val_pred),
                'train_precision': precision_score(y_train, train_pred),
                'val_precision': precision_score(y_val, val_pred),
                'train_recall': recall_score(y_train, train_pred),
                'val_recall': recall_score(y_val, val_pred),
                'train_f1': f1_score(y_train, train_pred),
                'val_f1': f1_score(y_val, val_pred)
            }
            
            # Save scaler
            joblib.dump(self.scaler, 'models/scaler.pkl')
            
            self.is_trained = True
            logger.info("Model training completed successfully")
            logger.info(f"Validation Accuracy: {metrics['val_accuracy']:.4f}")
            logger.info(f"Validation F1-Score: {metrics['val_f1']:.4f}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error during model training: {e}")
            return {}
    
    def predict(self, data: pd.DataFrame) -> Dict:
        """Generate trading signals using the trained model"""
        try:
            if not self.is_trained and self.model is None:
                self.load_model()
            
            if self.model is None:
                logger.error("No trained model available")
                return {}
            
            # Prepare data for prediction
            features = data[self.feature_columns].values
            features_scaled = self.scaler.transform(features)
            
            # Create sequence for prediction (use last sequence_length points)
            if len(features_scaled) < self.sequence_length:
                logger.error(f"Not enough data points. Need {self.sequence_length}, got {len(features_scaled)}")
                return {}
            
            X = features_scaled[-self.sequence_length:].reshape(1, self.sequence_length, -1)
            
            # Make prediction
            prediction_prob = self.model.predict(X)[0][0]
            prediction = 1 if prediction_prob > settings.SIGNAL_THRESHOLD else 0
            confidence = prediction_prob if prediction == 1 else (1 - prediction_prob)
            
            # Generate signal
            signal_type = "BUY" if prediction == 1 else "SELL"
            
            result = {
                'signal': signal_type,
                'confidence': float(confidence),
                'probability': float(prediction_prob),
                'timestamp': pd.Timestamp.now().isoformat(),
                'model_type': self.model_type
            }
            
            logger.info(f"Generated signal: {signal_type} (confidence: {confidence:.3f})")
            return result
            
        except Exception as e:
            logger.error(f"Error during prediction: {e}")
            return {}
    
    def load_model(self):
        """Load trained model and scaler"""
        try:
            if os.path.exists(settings.MODEL_PATH):
                self.model = tf.keras.models.load_model(settings.MODEL_PATH)
                logger.info("Model loaded successfully")
                
            if os.path.exists('models/scaler.pkl'):
                self.scaler = joblib.load('models/scaler.pkl')
                logger.info("Scaler loaded successfully")
                
            self.is_trained = True
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
    
    def save_model(self, path: str = None):
        """Save the trained model"""
        try:
            save_path = path or settings.MODEL_PATH
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            if self.model:
                self.model.save(save_path)
                logger.info(f"Model saved to {save_path}")
                
        except Exception as e:
            logger.error(f"Error saving model: {e}")


if __name__ == "__main__":
    # Test the AI model
    from data_collector import XAUUSDDataCollector
    
    logger.info("Testing AI Model...")
    
    # Collect data
    collector = XAUUSDDataCollector()
    data = collector.get_processed_data(timeframe="1h")
    
    if not data.empty:
        # Initialize and train model
        predictor = XAUUSDPredictor(model_type="lstm")
        metrics = predictor.train(data)
        
        if metrics:
            logger.info("Model training successful!")
            logger.info(f"Metrics: {metrics}")
            
            # Test prediction
            prediction = predictor.predict(data.tail(100))
            logger.info(f"Test prediction: {prediction}")
        else:
            logger.error("Model training failed")
    else:
        logger.error("No data available for testing")
