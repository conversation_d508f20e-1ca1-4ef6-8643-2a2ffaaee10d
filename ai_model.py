"""
AI Model for XAUUSD Trading Signal Generation (PyTorch Implementation)
"""
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from typing import Tuple, Dict, List, Optional
import joblib
import os
from loguru import logger
from config import settings


# PyTorch Model Definitions
class LSTMModel(nn.Module):
    """LSTM model for XAUUSD prediction"""

    def __init__(self, input_size, hidden_size=128, num_layers=3, dropout=0.2):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                           batch_first=True, dropout=dropout)
        self.dropout = nn.Dropout(dropout)
        self.fc1 = nn.Linear(hidden_size, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, 1)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        # Take the last output
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        out = torch.relu(self.fc1(out))
        out = self.dropout(out)
        out = torch.relu(self.fc2(out))
        out = self.sigmoid(self.fc3(out))
        return out


class TransformerModel(nn.Module):
    """Transformer model for XAUUSD prediction"""

    def __init__(self, input_size, d_model=128, nhead=8, num_layers=4, dropout=0.1):
        super(TransformerModel, self).__init__()
        self.input_projection = nn.Linear(input_size, d_model)
        self.positional_encoding = nn.Parameter(torch.randn(1000, d_model))

        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, nhead=nhead, dropout=dropout, batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

        self.fc1 = nn.Linear(d_model, 64)
        self.fc2 = nn.Linear(64, 1)
        self.dropout = nn.Dropout(dropout)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        seq_len = x.size(1)
        x = self.input_projection(x)
        x = x + self.positional_encoding[:seq_len, :].unsqueeze(0)

        x = self.transformer(x)
        # Global average pooling
        x = x.mean(dim=1)

        x = self.dropout(x)
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.sigmoid(self.fc2(x))
        return x


class XAUUSDPredictor:
    """AI Model for XAUUSD trading signal prediction"""

    def __init__(self, model_type: str = "lstm"):
        self.model_type = model_type
        self.sequence_length = settings.SEQUENCE_LENGTH
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.is_trained = False
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
    def prepare_sequences(self, data: pd.DataFrame, target_col: str = 'binary_signal') -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for LSTM/Transformer training"""
        try:
            # Select feature columns (exclude target and non-numeric columns)
            exclude_cols = ['datetime', 'signal', 'binary_signal', 'price_movement']
            self.feature_columns = [col for col in data.columns if col not in exclude_cols]
            
            # Prepare features and target
            features = data[self.feature_columns].values
            target = data[target_col].values
            
            # Scale features
            features_scaled = self.scaler.fit_transform(features)
            
            # Create sequences
            X, y = [], []
            for i in range(self.sequence_length, len(features_scaled)):
                X.append(features_scaled[i-self.sequence_length:i])
                y.append(target[i])
            
            X = np.array(X)
            y = np.array(y)
            
            logger.info(f"Prepared sequences: X shape {X.shape}, y shape {y.shape}")
            return X, y
            
        except Exception as e:
            logger.error(f"Error preparing sequences: {e}")
            return np.array([]), np.array([])
    
    def build_model(self, input_size: int):
        """Build PyTorch model"""
        try:
            if self.model_type == "lstm":
                model = LSTMModel(input_size=input_size)
            elif self.model_type == "transformer":
                model = TransformerModel(input_size=input_size)
            else:
                raise ValueError(f"Unsupported model type: {self.model_type}")

            model = model.to(self.device)
            logger.info(f"{self.model_type.upper()} model built successfully")
            return model

        except Exception as e:
            logger.error(f"Error building {self.model_type} model: {e}")
            return None
    
    def train_epoch(self, model, dataloader, criterion, optimizer):
        """Train model for one epoch"""
        model.train()
        total_loss = 0
        correct = 0
        total = 0

        for batch_x, batch_y in dataloader:
            batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)

            optimizer.zero_grad()
            outputs = model(batch_x)
            loss = criterion(outputs.squeeze(), batch_y.float())
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            predicted = (outputs.squeeze() > 0.5).float()
            total += batch_y.size(0)
            correct += (predicted == batch_y).sum().item()

        return total_loss / len(dataloader), correct / total
    
    def validate_epoch(self, model, dataloader, criterion):
        """Validate model for one epoch"""
        model.eval()
        total_loss = 0
        correct = 0
        total = 0

        with torch.no_grad():
            for batch_x, batch_y in dataloader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)

                outputs = model(batch_x)
                loss = criterion(outputs.squeeze(), batch_y.float())

                total_loss += loss.item()
                predicted = (outputs.squeeze() > 0.5).float()
                total += batch_y.size(0)
                correct += (predicted == batch_y).sum().item()

        return total_loss / len(dataloader), correct / total

    def train(self, data: pd.DataFrame, validation_split: float = 0.2) -> Dict:
        """Train the AI model"""
        try:
            # Prepare data
            X, y = self.prepare_sequences(data)

            if len(X) == 0:
                logger.error("No data available for training")
                return {}

            # Split data
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=validation_split, random_state=42, stratify=y
            )

            # Convert to PyTorch tensors
            X_train = torch.FloatTensor(X_train)
            X_val = torch.FloatTensor(X_val)
            y_train = torch.LongTensor(y_train)
            y_val = torch.LongTensor(y_val)

            # Create data loaders
            train_dataset = TensorDataset(X_train, y_train)
            val_dataset = TensorDataset(X_val, y_val)
            train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)

            # Build model
            input_size = X.shape[2]
            self.model = self.build_model(input_size)

            if self.model is None:
                logger.error("Failed to build model")
                return {}

            # Loss and optimizer
            criterion = nn.BCELoss()
            optimizer = optim.Adam(self.model.parameters(), lr=0.001)
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)

            # Training loop
            logger.info("Starting model training...")
            best_val_acc = 0
            patience = 10
            patience_counter = 0

            for epoch in range(100):
                train_loss, train_acc = self.train_epoch(self.model, train_loader, criterion, optimizer)
                val_loss, val_acc = self.validate_epoch(self.model, val_loader, criterion)

                scheduler.step(val_loss)

                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, "
                              f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")

                # Early stopping
                if val_acc > best_val_acc:
                    best_val_acc = val_acc
                    patience_counter = 0
                    # Save best model
                    torch.save(self.model.state_dict(), settings.MODEL_PATH)
                else:
                    patience_counter += 1
                    if patience_counter >= patience:
                        logger.info(f"Early stopping at epoch {epoch}")
                        break

            # Load best model
            self.model.load_state_dict(torch.load(settings.MODEL_PATH))

            # Final evaluation
            train_pred = self._predict_batch(X_train)
            val_pred = self._predict_batch(X_val)

            metrics = {
                'train_accuracy': accuracy_score(y_train.numpy(), train_pred),
                'val_accuracy': accuracy_score(y_val.numpy(), val_pred),
                'train_precision': precision_score(y_train.numpy(), train_pred),
                'val_precision': precision_score(y_val.numpy(), val_pred),
                'train_recall': recall_score(y_train.numpy(), train_pred),
                'val_recall': recall_score(y_val.numpy(), val_pred),
                'train_f1': f1_score(y_train.numpy(), train_pred),
                'val_f1': f1_score(y_val.numpy(), val_pred)
            }

            # Save scaler
            joblib.dump(self.scaler, 'models/scaler.pkl')

            self.is_trained = True
            logger.info("Model training completed successfully")
            logger.info(f"Validation Accuracy: {metrics['val_accuracy']:.4f}")
            logger.info(f"Validation F1-Score: {metrics['val_f1']:.4f}")

            return metrics

        except Exception as e:
            logger.error(f"Error during model training: {e}")
            return {}
    
    def _predict_batch(self, X):
        """Helper method for batch prediction"""
        self.model.eval()
        with torch.no_grad():
            X = X.to(self.device)
            outputs = self.model(X)
            predictions = (outputs.squeeze() > 0.5).cpu().numpy().astype(int)
        return predictions

    def predict(self, data: pd.DataFrame) -> Dict:
        """Generate trading signals using the trained model"""
        try:
            if not self.is_trained and self.model is None:
                self.load_model()

            if self.model is None:
                logger.error("No trained model available")
                return {}

            # Prepare data for prediction
            features = data[self.feature_columns].values
            features_scaled = self.scaler.transform(features)

            # Create sequence for prediction (use last sequence_length points)
            if len(features_scaled) < self.sequence_length:
                logger.error(f"Not enough data points. Need {self.sequence_length}, got {len(features_scaled)}")
                return {}

            X = features_scaled[-self.sequence_length:].reshape(1, self.sequence_length, -1)
            X = torch.FloatTensor(X).to(self.device)

            # Make prediction
            self.model.eval()
            with torch.no_grad():
                prediction_prob = self.model(X).cpu().numpy()[0][0]

            prediction = 1 if prediction_prob > settings.SIGNAL_THRESHOLD else 0
            confidence = prediction_prob if prediction == 1 else (1 - prediction_prob)

            # Generate signal
            signal_type = "BUY" if prediction == 1 else "SELL"

            result = {
                'signal': signal_type,
                'confidence': float(confidence),
                'probability': float(prediction_prob),
                'timestamp': pd.Timestamp.now().isoformat(),
                'model_type': self.model_type
            }

            logger.info(f"Generated signal: {signal_type} (confidence: {confidence:.3f})")
            return result

        except Exception as e:
            logger.error(f"Error during prediction: {e}")
            return {}
    
    def load_model(self):
        """Load trained model and scaler"""
        try:
            if os.path.exists(settings.MODEL_PATH):
                # We need to know the input size to rebuild the model
                # For now, we'll use a default and update this when we have data
                if hasattr(self, 'feature_columns') and self.feature_columns:
                    input_size = len(self.feature_columns)
                else:
                    input_size = 20  # Default size, will be updated during prediction

                self.model = self.build_model(input_size)
                if self.model:
                    self.model.load_state_dict(torch.load(settings.MODEL_PATH, map_location=self.device))
                    self.model.eval()
                    logger.info("Model loaded successfully")

            if os.path.exists('models/scaler.pkl'):
                self.scaler = joblib.load('models/scaler.pkl')
                logger.info("Scaler loaded successfully")

            self.is_trained = True

        except Exception as e:
            logger.error(f"Error loading model: {e}")

    def save_model(self, path: str = None):
        """Save the trained model"""
        try:
            save_path = path or settings.MODEL_PATH
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            if self.model:
                torch.save(self.model.state_dict(), save_path)
                logger.info(f"Model saved to {save_path}")

        except Exception as e:
            logger.error(f"Error saving model: {e}")


if __name__ == "__main__":
    # Test the AI model
    from data_collector import XAUUSDDataCollector
    
    logger.info("Testing AI Model...")
    
    # Collect data
    collector = XAUUSDDataCollector()
    data = collector.get_processed_data(timeframe="1h")
    
    if not data.empty:
        # Initialize and train model
        predictor = XAUUSDPredictor(model_type="lstm")
        metrics = predictor.train(data)
        
        if metrics:
            logger.info("Model training successful!")
            logger.info(f"Metrics: {metrics}")
            
            # Test prediction
            prediction = predictor.predict(data.tail(100))
            logger.info(f"Test prediction: {prediction}")
        else:
            logger.error("Model training failed")
    else:
        logger.error("No data available for testing")
