2025-06-13 19:55:40.463 | INFO     | api_server:startup_event:76 - Starting XAUUSD AI Trading Signal API...
2025-06-13 19:55:40.464 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:55:40.464 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:55:40.464 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:55:40.464 | INFO     | signal_generator:initialize_models:38 - Initializing AI models...
2025-06-13 19:55:40.464 | INFO     | signal_generator:initialize_models:41 - Loading model for 15m timeframe
2025-06-13 19:55:40.467 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 19:55:40.472 | ERROR    | ai_model:load_model:369 - Error loading model: Error(s) in loading state_dict for LSTMModel:
	size mismatch for lstm.weight_ih_l0: copying a param with shape torch.Size([512, 39]) from checkpoint, the shape in current model is torch.Size([512, 20]).
2025-06-13 19:55:40.473 | INFO     | signal_generator:initialize_models:41 - Loading model for 1h timeframe
2025-06-13 19:55:40.475 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 19:55:40.478 | ERROR    | ai_model:load_model:369 - Error loading model: Error(s) in loading state_dict for LSTMModel:
	size mismatch for lstm.weight_ih_l0: copying a param with shape torch.Size([512, 39]) from checkpoint, the shape in current model is torch.Size([512, 20]).
2025-06-13 19:55:40.478 | INFO     | signal_generator:initialize_models:41 - Loading model for 4h timeframe
2025-06-13 19:55:40.480 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 19:55:40.483 | ERROR    | ai_model:load_model:369 - Error loading model: Error(s) in loading state_dict for LSTMModel:
	size mismatch for lstm.weight_ih_l0: copying a param with shape torch.Size([512, 39]) from checkpoint, the shape in current model is torch.Size([512, 20]).
2025-06-13 19:55:40.483 | INFO     | signal_generator:initialize_models:44 - All models initialized successfully
2025-06-13 19:55:40.484 | INFO     | api_server:startup_event:83 - API server started successfully
2025-06-13 19:57:26.070 | INFO     | signal_generator:generate_signal:54 - Generating signal for 1h timeframe
2025-06-13 19:57:26.712 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 19:57:26.768 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 19:57:26.773 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 19:57:26.777 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 19:57:26.781 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 19:57:26.782 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 19:57:26.782 | ERROR    | ai_model:predict:342 - Error during prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-13 19:57:26.782 | WARNING  | signal_generator:generate_signal:71 - No prediction generated for 1h
2025-06-13 19:57:29.048 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 19:57:29.104 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 19:57:29.109 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 19:57:29.112 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 19:57:29.116 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 19:57:29.116 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
