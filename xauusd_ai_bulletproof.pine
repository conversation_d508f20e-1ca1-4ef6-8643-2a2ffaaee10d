//@version=5
indicator("XAUUSD AI Signals", shorttitle="AI", overlay=true, max_labels_count=100, max_lines_count=50)

// Input Settings
ai_enabled = input.bool(true, "Enable AI Signals")
show_buy = input.bool(true, "Show Buy Signals")
show_sell = input.bool(true, "Show Sell Signals")
min_conf = input.float(0.6, "Min Confidence", 0.1, 1.0, 0.1)
max_daily = input.int(5, "Max Daily Signals", 1, 20)

// Colors
buy_col = input.color(color.green, "Buy Color")
sell_col = input.color(color.red, "Sell Color")

// Technical Indicators
sma20 = ta.sma(close, 20)
sma50 = ta.sma(close, 50)
rsi = ta.rsi(close, 14)
[macd, signal, hist] = ta.macd(close, 12, 26, 9)
atr = ta.atr(14)

// Market Analysis
bullish = close > sma50
bearish = close < sma50
oversold = rsi < 30
overbought = rsi > 70
macd_bull = macd > signal
macd_bear = macd < signal

// Signal Logic
buy_cond = bullish and macd_bull and oversold
sell_cond = bearish and macd_bear and overbought

// Confidence Calculation
conf_base = 0.5
conf_trend = bullish ? 0.2 : bearish ? -0.2 : 0.0
conf_macd = macd_bull ? 0.15 : macd_bear ? -0.15 : 0.0
conf_rsi = oversold or overbought ? 0.15 : 0.0

confidence = math.abs(conf_base + conf_trend + conf_macd + conf_rsi)
confidence := math.min(confidence, 1.0)

// Daily Signal Counter
var int daily_count = 0
if dayofweek != dayofweek[1]
    daily_count := 0

// Signal Generation
var bool show_buy_signal = false
var bool show_sell_signal = false
var float signal_conf = 0.0

if ai_enabled and daily_count < max_daily and confidence >= min_conf
    if buy_cond and not buy_cond[1] and show_buy
        show_buy_signal := true
        signal_conf := confidence
        daily_count += 1
    else if sell_cond and not sell_cond[1] and show_sell
        show_sell_signal := true
        signal_conf := confidence
        daily_count += 1
    else
        show_buy_signal := false
        show_sell_signal := false

// Reset signals after one bar
if show_buy_signal[1] or show_sell_signal[1]
    show_buy_signal := false
    show_sell_signal := false

// Plot Moving Averages
plot(sma20, "SMA20", color.orange, 1)
plot(sma50, "SMA50", color.purple, 2)

// Plot Signals
plotshape(show_buy_signal, "Buy", shape.labelup, location.belowbar, buy_col, text="BUY", textcolor=color.white, size=size.normal)
plotshape(show_sell_signal, "Sell", shape.labeldown, location.abovebar, sell_col, text="SELL", textcolor=color.white, size=size.normal)

// Confidence Labels
if show_buy_signal or show_sell_signal
    conf_text = str.tostring(signal_conf * 100, "#") + "%"
    label.new(bar_index, show_buy_signal ? low - atr : high + atr, conf_text, 
              color=color.blue, textcolor=color.white, size=size.small,
              style=show_buy_signal ? label.style_label_up : label.style_label_down)

// Info Table
if barstate.islast
    var t = table.new(position.top_right, 2, 4, bgcolor=color.white, border_width=1)
    table.cell(t, 0, 0, "AI Status", text_color=color.black)
    table.cell(t, 1, 0, ai_enabled ? "ON" : "OFF", text_color=ai_enabled ? color.green : color.red)
    table.cell(t, 0, 1, "Signals", text_color=color.black)
    table.cell(t, 1, 1, str.tostring(daily_count), text_color=color.black)
    table.cell(t, 0, 2, "RSI", text_color=color.black)
    table.cell(t, 1, 2, str.tostring(rsi, "#"), text_color=rsi > 70 ? color.red : rsi < 30 ? color.green : color.black)
    table.cell(t, 0, 3, "Trend", text_color=color.black)
    table.cell(t, 1, 3, bullish ? "UP" : "DOWN", text_color=bullish ? color.green : color.red)

// Alerts
alertcondition(show_buy_signal, "AI Buy", "XAUUSD AI BUY Signal")
alertcondition(show_sell_signal, "AI Sell", "XAUUSD AI SELL Signal")
